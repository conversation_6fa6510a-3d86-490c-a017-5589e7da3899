import React, { Suspense } from "react";
import { EditableField } from "./EditableField";
import { useFormContext } from "react-hook-form";
import { Options } from "../../../../utils/consts";
import { formatEIN, formatSSN, parseEIN, parseSSN } from "../../../../utils/formatters";
import { formatDate } from "../../../../utils/dateUtils";

const SignatureMethod = React.lazy(() => import("./SignatureMethod"));

const formatStateName = (stateCode) => {
  if (!stateCode) return "";
  const state = Options.state.find((s) => s.value === stateCode);
  return state ? state.label : stateCode;
};

export const SignApplication = ({ appId }) => {
  const { watch } = useFormContext();

  const owners = watch("owners");

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold my-8 text-center">Sign Your Application</h3>

      <div className="bg-white border border-slate-200 rounded-lg shadow-xl max-w-5xl mx-auto">
        {/* Body */}
        <div className="px-8 py-5">
          <div className="space-y-6">
            {/* Business Information - Full Width */}
            <div className=" p-5 rounded-lg border border-slate-200 shadow-sm">
              <h4 className="text-xl font-bold mb-3 text-slate-800 border-b border-slate-300 pb-2">
                Business Information
              </h4>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-6 gap-y-2 text-sm">
                <div className="flex flex-col">
                  <EditableField label={"Business Legal Name:"} fieldName="businessName" />
                </div>
                <div className="flex flex-col">
                  <EditableField label={"Business DBA Name:"} fieldName="dbaName" />
                </div>
                <div className="flex flex-col">
                  <EditableField label={"Address:"} fieldName="address.line1" />
                </div>
                <div className="flex flex-col">
                  <EditableField label={"Address Line 2:"} fieldName="address.line2" />
                </div>
                <div className="flex flex-col">
                  <EditableField label={"City:"} fieldName="address.city" />
                </div>
                <div className="flex flex-col">
                  <EditableField
                    label={"State:"}
                    type={"select"}
                    options={Options.state}
                    fieldName="address.state"
                    formatter={formatStateName}
                  />
                </div>
                <div className="flex flex-col">
                  <EditableField
                    label={"Zip:"}
                    fieldName="address.zip"
                    inputProps={{ maxLength: 5, inputMode: "numeric" }}
                  />
                </div>

                <div className="flex flex-col">
                  <EditableField
                    label={"Legal Entity:"}
                    type={"select"}
                    options={Options.entityType}
                    fieldName="entityType"
                  />
                </div>
                <div className="flex flex-col">
                  <EditableField label={"Industry:"} type={"select"} options={Options.industry} fieldName="industry" />
                </div>
                <div className="flex flex-col">
                  <EditableField
                    label={"Business Start Date:"}
                    type={"date"}
                    fieldName="businessStartDate"
                    formatter={formatDate}
                  />
                </div>
                <div className="flex flex-col">
                  <EditableField
                    label={"EIN:"}
                    fieldName="ein"
                    formatter={formatEIN}
                    inputProps={{ inputMode: "numeric", maxLength: "10", formatter: formatEIN, parser: parseEIN }}
                  />
                </div>
              </div>
            </div>

            {/* Owner Sections */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Owner 1 Information */}
              {owners.map((_o, index) => {
                const ownerKey = `owners.${index}.`;
                return (
                  <div className="p-5 rounded-lg border border-slate-300 shadow-sm">
                    <h4 className="text-xl font-bold mb-3 text-slate-800 border-b border-slate-300 pb-2">
                      Owner {index + 1} Information
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex flex-col">
                        <EditableField label={"First Name:"} fieldName={ownerKey + "firstName"} />
                      </div>
                      <div className="flex flex-col">
                        <EditableField label={"Last Name:"} fieldName={ownerKey + "lastName"} />
                      </div>
                      <div className="flex flex-col">
                        <EditableField label={"Address:"} fieldName={ownerKey + "address.line1"} />
                      </div>
                      <div className="flex flex-col">
                        <EditableField label={"Address Line 2:"} fieldName={ownerKey + "address.line2"} />
                      </div>
                      <div className="flex flex-col">
                        <EditableField label={"City:"} fieldName={ownerKey + "address.city"} />
                      </div>
                      <div className="flex flex-col">
                        <EditableField
                          label={"State:"}
                          type={"select"}
                          options={Options.state}
                          fieldName={ownerKey + "address.state"}
                          formatter={formatStateName}
                        />
                      </div>
                      <div className="flex flex-col">
                        <EditableField
                          label={"Zip:"}
                          fieldName={ownerKey + "address.zip"}
                          inputProps={{ maxLength: 5, inputMode: "numeric" }}
                        />
                      </div>
                      <div className="flex flex-col">
                        <EditableField
                          label={"Ownership Percent:"}
                          fieldName={ownerKey + "ownershipPercentage"}
                          formatter={(value) => `${value}%`}
                        />
                      </div>
                      <div className="flex flex-col">
                        <EditableField
                          type={"date"}
                          label={"Date of Birth:"}
                          fieldName={ownerKey + "dateOfBirth"}
                          formatter={formatDate}
                        />
                      </div>
                      <div className="flex flex-col">
                        <EditableField
                          label={"SSN:"}
                          formatter={formatSSN}
                          fieldName={ownerKey + "ssn"}
                          inputProps={{
                            formatter: formatSSN,
                            parser: parseSSN,
                            maxLength: "11",
                            inputMode: "numeric",
                          }}
                        />
                      </div>
                      {index == 0 && (
                        <div className="flex flex-col">
                          <EditableField
                            label={"Credit Score:"}
                            type={"select"}
                            options={Options.fico}
                            fieldName={ownerKey + "creditScore"}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-slate-200 px-8 py-5 bg-gradient-to-r from-slate-50 to-white">
          <div className="space-y-5">
            {/* Signature Section */}
            <Suspense fallback={null}>
              <SignatureMethod />
            </Suspense>

            {/* Disclaimer */}
            <div className="text-sm text-slate-700 bg-amber-50 p-4 rounded-lg border-l-4 border-amber-400">
              <p className="leading-relaxed">
                By signing, I acknowledge that I have read and understand the terms and conditions of this funding
                application. I certify that all information provided is true and accurate to the best of my knowledge. I
                understand that this application does not guarantee funding approval and that additional documentation
                may be required.
              </p>
            </div>

            {/* Application UUID */}
            <div className="text-xs text-slate-500 mt-4 p-2 bg-slate-100 rounded border font-mono">
              Application UUID: {appId}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
